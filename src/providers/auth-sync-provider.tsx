"use client";

import { useEffect, useState } from 'react';
import { useAuthSync } from '@/hooks/useAuthSync';
import { useAuthStore } from '@/store/useAuthStore';

export function AuthSyncProvider({ children }: { children: React.ReactNode }) {
  const [isHydrated, setIsHydrated] = useState(false);
  const { isAuthenticated, user } = useAuthStore();

  useEffect(() => {
    // Hydrate immediately để không block rendering
    setIsHydrated(true);

    const initAuth = async () => {
      try {
        // Nếu đã có auth state từ localStorage, không cần check server
        if (isAuthenticated && user) {
          console.log('✅ Auth state already available from localStorage');
          return;
        }

        // Check server session trong background - không block UI
        console.log('🔍 Background auth check...');

        const controller = new AbortController();
        // Giảm timeout xuống 1.5s cho Vercel free tier
        const timeoutId = setTimeout(() => controller.abort(), 1500);

        try {
          const response = await fetch('/api/auth/init', {
            method: 'GET',
            credentials: 'include',
            signal: controller.signal,
            headers: {
              'Cache-Control': 'no-cache',
            },
          });

          clearTimeout(timeoutId);

          if (response.ok) {
            const data = await response.json();
            console.log('✅ Found existing session, updating auth state');

            useAuthStore.setState({
              user: data.user,
              isAuthenticated: true,
              accessToken: null // Will be refreshed automatically
            });
          } else {
            console.log('ℹ️ No valid session found');
            // Clear any stale state
            if (isAuthenticated) {
              useAuthStore.setState({
                user: null,
                isAuthenticated: false,
                accessToken: null
              });
            }
          }
        } catch (fetchError) {
          clearTimeout(timeoutId);
          
          if (fetchError instanceof Error && fetchError.name === 'AbortError') {
            console.log('⏰ Auth check timeout (1.5s) - using cached state');
          } else {
            console.warn('⚠️ Auth check failed:', fetchError instanceof Error ? fetchError.message : 'Unknown error');
          }
        }
      } catch (error) {
        console.error('❌ Auth init error:', error);
      }
    };

    // Run auth check in background, non-blocking
    initAuth();
  }, []); // Only run once on mount

  useAuthSync();

  // Always render children immediately - no loading state
  // Auth check happens in background and updates state when ready
  return <>{children}</>;
}