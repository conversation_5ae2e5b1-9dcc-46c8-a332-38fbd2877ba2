@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Highlight.js theme */
@import '../styles/highlight-theme.css';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    
    /* Custom colors */
    --navy-800: 30 41 99;
    --navy-900: 20 29 72;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    overflow-x: hidden;
  }
}

/* Prose Styles */
.prose {
  @apply max-w-none text-gray-700 dark:text-gray-300;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4 {
  @apply scroll-mt-24 font-bold text-gray-900 dark:text-gray-100;
}

.prose h2 {
  @apply text-2xl mt-8 mb-4;
}

.prose h3 {
  @apply text-xl mt-6 mb-3;
}

.prose h4 {
  @apply text-lg mt-4 mb-2;
}

.prose p {
  @apply mb-4 leading-relaxed;
}

.prose ul {
  @apply mb-4 space-y-2 list-none;
}

.prose ul li {
  @apply relative pl-6 text-gray-700 dark:text-gray-300;
}

.prose ul li::before {
  content: "—";
  @apply absolute left-0 text-gray-400;
}

.prose img {
  @apply rounded-lg shadow-lg my-8;
}

/* Smooth Scroll */
html {
  scroll-behavior: smooth;
}

/* Code Block Styles are now handled by highlight-theme.css */

.mask-linear-gradient {
  mask-image: linear-gradient(to right, black 85%, transparent 100%);
  -webkit-mask-image: linear-gradient(to right, black 85%, transparent 100%);
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

@layer utilities {
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* Custom bullet for prose - override default list style */
.prose ul {
  list-style: none !important;
  padding-left: 0 !important;
}

.prose ul > li {
  position: relative;
  padding-left: 1.5rem !important;
  margin-bottom: 0.5rem;
}

.prose ul > li::before {
  content: '•';
  position: absolute;
  left: 0;
  top: 0;
  color: #6b7280;
  font-size: 1.2em;
  font-weight: bold;
}

/* Dark mode support */
.dark .prose ul > li::before {
  color: #9ca3af;
}

/* Remove default marker */
.prose ul > li::marker {
  display: none;
}