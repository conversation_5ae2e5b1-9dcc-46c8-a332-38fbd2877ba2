/* Prism.js Tomorrow Night Theme */
/* Based on https://github.com/chriskempson/tomorrow-theme */

code[class*="language-"],
pre[class*="language-"] {
  color: #ccc;
  background: none;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}

/* Code blocks */
pre[class*="language-"] {
  padding: 1em;
  margin: .5em 0;
  overflow: auto;
  background: #2d3748;
  border-radius: 0.5rem;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
  background: #2d3748;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
  padding: .1em;
  border-radius: .3em;
  white-space: normal;
}

.token.comment,
.token.block-comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #999;
}

.token.punctuation {
  color: #ccc;
}

.token.tag,
.token.attr-name,
.token.namespace,
.token.deleted {
  color: #e2777a;
}

.token.function-name {
  color: #6196cc;
}

.token.boolean,
.token.number,
.token.function {
  color: #f08d49;
}

.token.property,
.token.class-name,
.token.constant,
.token.symbol {
  color: #f8c555;
}

.token.selector,
.token.important,
.token.atrule,
.token.keyword,
.token.builtin {
  color: #cc99cd;
}

.token.string,
.token.char,
.token.attr-value,
.token.regex,
.token.variable {
  color: #7ec699;
}

.token.operator,
.token.entity,
.token.url {
  color: #67cdcc;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.entity {
  cursor: help;
}

.token.inserted {
  color: green;
}

/* Language-specific styling */
.language-css .token.string,
.style .token.string {
  color: #87c38a;
}

.language-javascript .token.keyword {
  color: #cc99cd;
}

.language-java .token.keyword {
  color: #cc99cd;
}

.language-python .token.keyword {
  color: #cc99cd;
}

/* Line highlighting */
.line-highlight {
  background: rgba(153, 122, 102, 0.08);
  background: linear-gradient(to right, rgba(153, 122, 102, 0.1) 70%, rgba(153, 122, 102, 0));
}

/* Responsive design */
@media (max-width: 768px) {
  pre[class*="language-"] {
    padding: 0.75em;
    font-size: 0.9em;
    overflow-x: auto;
  }
}

/* Dark mode compatibility */
@media (prefers-color-scheme: dark) {
  code[class*="language-"],
  pre[class*="language-"] {
    color: #e2e8f0;
  }
  
  pre[class*="language-"] {
    background: #1a202c;
  }
  
  :not(pre) > code[class*="language-"],
  pre[class*="language-"] {
    background: #1a202c;
  }
}

/* Fallback styles for when JavaScript is disabled */
.no-js pre[class*="language-"] {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
}
