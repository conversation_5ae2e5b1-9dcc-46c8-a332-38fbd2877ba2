/* Dracula Theme for Highlight.js - VSCode Style */
/* Optimized for Vercel deployment and stable rendering */

.hljs {
  color: #f8f8f2;
  background: #282a36;
  font-family: 'Fira Code', 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', <PERSON><PERSON><PERSON>, 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #44475a;
  overflow-x: auto;
  margin: 1.5rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Inline code */
.hljs-inline {
  background: #282a36 !important;
  color: #f8f8f2 !important;
  padding: 0.2rem 0.4rem !important;
  border-radius: 4px !important;
  border: 1px solid #44475a !important;
  font-family: 'Fira Code', 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', <PERSON><PERSON><PERSON>, 'Courier New', monospace !important;
  font-size: 0.875em !important;
  margin: 0 !important;
  white-space: nowrap;
}

/* === DRACULA COLOR SCHEME === */

/* Comments */
.hljs-comment,
.hljs-quote,
.hljs-doctag {
  color: #6272a4;
  font-style: italic;
}

/* Keywords */
.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-section,
.hljs-link {
  color: #ff79c6;
  font-weight: 500;
}

/* Functions */
.hljs-function .hljs-keyword,
.hljs-title,
.hljs-name {
  color: #50fa7b;
}

/* Strings */
.hljs-string,
.hljs-bullet,
.hljs-subst,
.hljs-symbol,
.hljs-addition {
  color: #f1fa8c;
}

/* Numbers */
.hljs-number,
.hljs-regexp,
.hljs-deletion {
  color: #bd93f9;
}

/* Variables */
.hljs-variable,
.hljs-template-variable,
.hljs-selector-attr,
.hljs-selector-pseudo {
  color: #f8f8f2;
}

/* Types, Classes */
.hljs-type,
.hljs-class .hljs-title,
.hljs-built_in,
.hljs-builtin-name {
  color: #8be9fd;
}

/* Attributes */
.hljs-attr,
.hljs-attribute {
  color: #50fa7b;
}

/* Meta */
.hljs-meta,
.hljs-meta-keyword {
  color: #ff79c6;
}

.hljs-meta-string {
  color: #f1fa8c;
}

/* Tags */
.hljs-tag {
  color: #ff79c6;
}

.hljs-tag .hljs-name {
  color: #ff79c6;
}

.hljs-tag .hljs-attr {
  color: #50fa7b;
}

/* === LANGUAGE SPECIFIC === */

/* JavaScript/TypeScript */
.hljs.language-javascript .hljs-keyword,
.hljs.language-typescript .hljs-keyword {
  color: #ff79c6;
}

.hljs.language-javascript .hljs-function,
.hljs.language-typescript .hljs-function {
  color: #50fa7b;
}

.hljs.language-javascript .hljs-built_in,
.hljs.language-typescript .hljs-built_in {
  color: #8be9fd;
}

/* Python */
.hljs.language-python .hljs-decorator {
  color: #ff79c6;
}

.hljs.language-python .hljs-keyword {
  color: #ff79c6;
}

.hljs.language-python .hljs-built_in {
  color: #8be9fd;
}

/* Java */
.hljs.language-java .hljs-keyword {
  color: #ff79c6;
}

.hljs.language-java .hljs-type {
  color: #8be9fd;
}

.hljs.language-java .hljs-annotation {
  color: #ff79c6;
}

/* CSS */
.hljs.language-css .hljs-selector-tag,
.hljs.language-css .hljs-selector-id,
.hljs.language-css .hljs-selector-class {
  color: #50fa7b;
}

.hljs.language-css .hljs-attribute {
  color: #8be9fd;
}

.hljs.language-css .hljs-built_in {
  color: #ff79c6;
}

/* JSON */
.hljs.language-json .hljs-attr {
  color: #8be9fd;
}

.hljs.language-json .hljs-string {
  color: #f1fa8c;
}

/* SQL */
.hljs.language-sql .hljs-keyword {
  color: #ff79c6;
}

.hljs.language-sql .hljs-built_in {
  color: #50fa7b;
}

/* Bash/Shell */
.hljs.language-bash .hljs-built_in,
.hljs.language-shell .hljs-built_in {
  color: #50fa7b;
}

.hljs.language-bash .hljs-variable,
.hljs.language-shell .hljs-variable {
  color: #8be9fd;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .hljs {
    padding: 1rem;
    font-size: 13px;
    margin: 1rem 0;
    border-radius: 6px;
  }
  
  .hljs-inline {
    font-size: 0.8rem !important;
  }
}

/* === FALLBACK STYLES === */
/* These ensure consistent appearance even when highlighting fails */
pre:not(.hljs) {
  background: #282a36 !important;
  color: #f8f8f2 !important;
  padding: 1.5rem !important;
  border-radius: 8px !important;
  border: 1px solid #44475a !important;
  overflow-x: auto !important;
  font-family: 'Fira Code', 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  margin: 1.5rem 0 !important;
}

code:not(.hljs):not(.hljs-inline) {
  background: #282a36 !important;
  color: #f8f8f2 !important;
  padding: 0.2rem 0.4rem !important;
  border-radius: 4px !important;
  border: 1px solid #44475a !important;
  font-family: 'Fira Code', 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
  font-size: 0.875em !important;
}

/* === PRINT STYLES === */
@media print {
  .hljs {
    background: #fff !important;
    color: #000 !important;
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }
  
  .hljs-inline {
    background: #f5f5f5 !important;
    color: #000 !important;
    border: 1px solid #ccc !important;
  }
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
  .hljs,
  .hljs-inline {
    transition: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .hljs {
    border-width: 2px !important;
  }
  
  .hljs-comment,
  .hljs-quote {
    color: #999 !important;
  }
}
