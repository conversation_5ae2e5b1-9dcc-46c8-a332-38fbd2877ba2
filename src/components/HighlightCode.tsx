'use client';

import { useEffect, useRef, useState } from 'react';
import hljs from 'highlight.js';

// Import core languages at build time để tránh động loading
import 'highlight.js/lib/languages/javascript';
import 'highlight.js/lib/languages/typescript';
import 'highlight.js/lib/languages/java';
import 'highlight.js/lib/languages/python';
import 'highlight.js/lib/languages/css';
import 'highlight.js/lib/languages/json';
import 'highlight.js/lib/languages/bash';
import 'highlight.js/lib/languages/xml'; // HTML
import 'highlight.js/lib/languages/sql';
import 'highlight.js/lib/languages/cpp';
import 'highlight.js/lib/languages/csharp';
import 'highlight.js/lib/languages/php';
import 'highlight.js/lib/languages/yaml';
import 'highlight.js/lib/languages/markdown';

interface HighlightCodeProps {
  content: string;
}

// Language mapping để map tên phổ biến
const LANGUAGE_MAP: { [key: string]: string } = {
  'js': 'javascript',
  'ts': 'typescript',
  'html': 'xml',
  'htm': 'xml',
  'sh': 'bash',
  'shell': 'bash',
  'cmd': 'bash',
  'batch': 'bash',
  'c++': 'cpp',
  'c#': 'csharp',
  'cs': 'csharp',
  'py': 'python',
  'yml': 'yaml',
  'dockerfile': 'bash', // fallback to bash
  'docker': 'bash',
  'ps1': 'bash', // fallback to bash
  'powershell': 'bash',
  'markup': 'xml',
  'plaintext': '',
  'text': ''
};

// Các ngôn ngữ được hỗ trợ để auto-detect
const SUPPORTED_LANGUAGES = [
  'javascript', 'typescript', 'java', 'python', 'css', 'json', 
  'bash', 'xml', 'sql', 'cpp', 'csharp', 'php', 'yaml', 'markdown'
];

export function HighlightCode({ content }: HighlightCodeProps) {
  const contentRef = useRef<HTMLDivElement>(null);
  const [isHighlighted, setIsHighlighted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!contentRef.current || isHighlighted) return;

    const highlightCodeBlocks = () => {
      try {
        const container = contentRef.current;
        if (!container) return;

        // Tìm tất cả code blocks
        const codeBlocks = container.querySelectorAll('pre code, code');
        
        if (codeBlocks.length === 0) {
          setIsHighlighted(true);
          return;
        }

        let processedCount = 0;

        codeBlocks.forEach((block) => {
          const codeElement = block as HTMLElement;
          
          try {
            // Skip nếu đã được process
            if (codeElement.classList.contains('hljs') || 
                codeElement.classList.contains('hljs-inline')) {
              processedCount++;
              return;
            }

            // Xác định loại: inline hay block
            const isInlineCode = !codeElement.closest('pre');
            
            if (isInlineCode) {
              // Inline code - chỉ thêm class styling
              codeElement.classList.add('hljs-inline');
              processedCount++;
              return;
            }

            // Block code - apply syntax highlighting
            const codeText = codeElement.textContent || '';
            if (!codeText.trim()) {
              processedCount++;
              return;
            }

            // Detect language từ class
            let detectedLanguage = '';
            const classList = codeElement.className.split(' ');
            
            for (const cls of classList) {
              if (cls.startsWith('language-')) {
                detectedLanguage = cls.replace('language-', '').toLowerCase();
                break;
              } else if (cls.startsWith('lang-')) {
                detectedLanguage = cls.replace('lang-', '').toLowerCase();
                break;
              }
            }

            // Map language name
            if (LANGUAGE_MAP[detectedLanguage]) {
              detectedLanguage = LANGUAGE_MAP[detectedLanguage];
            }

            let result;

            if (detectedLanguage && hljs.getLanguage(detectedLanguage)) {
              // Highlight với ngôn ngữ cụ thể
              result = hljs.highlight(codeText, { language: detectedLanguage });
              codeElement.innerHTML = result.value;
              codeElement.classList.add('hljs', `language-${detectedLanguage}`);
            } else if (detectedLanguage === '' || detectedLanguage === 'plaintext' || detectedLanguage === 'text') {
              // Plain text - không highlight
              codeElement.classList.add('hljs');
            } else {
              // Auto-detect với limited set để tránh chậm
              result = hljs.highlightAuto(codeText, SUPPORTED_LANGUAGES);
              codeElement.innerHTML = result.value;
              codeElement.classList.add('hljs');
              
              if (result.language) {
                codeElement.classList.add(`language-${result.language}`);
              }
            }

            processedCount++;

          } catch (blockError) {
            console.warn('Error highlighting individual block:', blockError);
            // Fallback: thêm hljs class để có styling
            codeElement.classList.add('hljs');
            processedCount++;
          }
        });

        console.log(`✅ Highlighted ${processedCount}/${codeBlocks.length} code blocks`);
        setIsHighlighted(true);
        setError(null);

      } catch (err) {
        console.error('❌ Code highlighting failed:', err);
        setError('Code highlighting failed');
        
        // Fallback: apply basic styling
        if (contentRef.current) {
          const codeBlocks = contentRef.current.querySelectorAll('pre code, code');
          codeBlocks.forEach((block) => {
            const codeElement = block as HTMLElement;
            if (!codeElement.classList.contains('hljs') && 
                !codeElement.classList.contains('hljs-inline')) {
              
              const isInline = !codeElement.closest('pre');
              codeElement.classList.add(isInline ? 'hljs-inline' : 'hljs');
            }
          });
        }
        
        setIsHighlighted(true);
      }
    };

    // Simple timeout approach - more reliable than requestIdleCallback
    const timer = setTimeout(highlightCodeBlocks, 50);

    return () => clearTimeout(timer);
  }, [content, isHighlighted]);

  // Show error state nếu có
  if (error) {
    console.warn('Highlighting error, using fallback styling');
  }

  return (
    <div className="relative">
      <div
        ref={contentRef}
        className="prose dark:prose-invert max-w-full prose-p:leading-relaxed prose-p:mb-4"
        dangerouslySetInnerHTML={{ __html: content }}
        suppressHydrationWarning={true}
      />
      
      {/* Simple loading indicator */}
      {!isHighlighted && (
        <div className="absolute top-2 right-2 opacity-30">
          <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
}
